#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
七鱼工单系统服务模块
提供工单创建、模板获取等功能
"""

import time
import hashlib
import json
import requests
from typing import Dict, List, Optional, Any
import config
from datetime import datetime


class QiyuTicketService:
    """七鱼工单系统服务类"""
    
    def __init__(self):
        """初始化七鱼工单服务"""
        self.app_key = config.QIYU_APPKEY
        self.app_secret = config.QIYU_ENCRYPT_KEY
        self.base_url = "https://qiyukf.com/openapi/v2"
        self.timeout = 30
        
        if not self.app_key or not self.app_secret:
            print("[ERROR] 七鱼工单系统配置缺失，请设置 QIYU_APPKEY 和 QIYU_APP_SECRET 环境变量")
    
    def _generate_checksum(self, request_json: str, time_stamp: int) -> str:
        """
        生成API请求的校验和
        
        Args:
            request_json (str): 请求JSON字符串
            time_stamp (int): 当前UTC时间戳
            
        Returns:
            str: SHA1校验和（小写十六进制）
        """
        # 计算请求JSON的MD5
        request_md5 = hashlib.md5(request_json.encode('utf-8')).hexdigest().lower()
        
        # 拼接字符串：appSecret + request_md5 + time
        raw_string = f"{self.app_secret}{request_md5}{time_stamp}"
        
        # SHA1哈希计算
        sha1_hash = hashlib.sha1(raw_string.encode('utf-8')).hexdigest()
        
        return sha1_hash.lower()
    
    def _make_request(self, endpoint: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        发送API请求
        
        Args:
            endpoint (str): API端点路径
            data (Dict[str, Any]): 请求数据
            
        Returns:
            Optional[Dict[str, Any]]: API响应结果
        """
        try:
            # 将数据转换为JSON字符串
            request_json = json.dumps(data, separators=(',', ':'))
            
            # 生成时间戳和校验和
            time_stamp = int(time.time())
            checksum = self._generate_checksum(request_json, time_stamp)
            
            # 构建完整URL
            url = f"{self.base_url}{endpoint}"
            params = {
                "appKey": self.app_key,
                "time": time_stamp,
                "checksum": checksum
            }
            
            # 发送请求
            headers = {
                "Content-Type": "application/json; charset=utf-8"
            }
            
            print(f"[DEBUG] ========== 七鱼工单API请求详情 ==========")
            print(f"[DEBUG] 端点: {endpoint}")
            print(f"[DEBUG] 请求URL: {url}")
            print(f"[DEBUG] 请求参数: {params}")
            print(f"[DEBUG] 请求头: {headers}")
            print(f"[DEBUG] 原始请求数据:")
            print(f"[DEBUG] {json.dumps(data, ensure_ascii=False, indent=2)}")
            print(f"[DEBUG] 请求JSON字符串: {request_json}")
            print(f"[DEBUG] 请求JSON长度: {len(request_json)} 字符")
            
            # 特别显示自定义字段信息
            if 'customFields' in data and data['customFields']:
                print(f"[DEBUG] ========== 自定义字段详情 ==========")
                for i, field in enumerate(data['customFields'], 1):
                    print(f"[DEBUG] 字段 {i}: ID={field.get('id')}, 值='{field.get('value')}'")
                print(f"[DEBUG] 自定义字段JSON:")
                print(f"[DEBUG] {json.dumps(data['customFields'], ensure_ascii=False, indent=2)}")
            else:
                print(f"[DEBUG] ⚠️  没有自定义字段数据")
            
            print(f"[DEBUG] ========== 发送请求 ==========")
            
            response = requests.post(
                url,
                params=params,
                headers=headers,
                data=request_json,  # 使用data而不是json参数
                timeout=self.timeout
            )
            
            print(f"[DEBUG] ========== 响应结果 ==========")
            print(f"[DEBUG] 响应状态码: {response.status_code}")
            print(f"[DEBUG] 响应头: {dict(response.headers)}")
            print(f"[DEBUG] 响应内容: {response.text}")
            print(f"[DEBUG] ==========================================")
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200:
                    return result
                else:
                    print(f"[ERROR] API返回错误: {result}")
                    return None
            else:
                print(f"[ERROR] HTTP请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"[ERROR] 七鱼API请求异常: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def create_ticket(self, 
                     title: str,
                     content: str,
                     uid: Optional[str] = None,
                     user_mobile: Optional[str] = None,
                     user_email: Optional[str] = None,
                     user_name: Optional[str] = None,
                     staff_id: Optional[int] = None,
                     priority: int = 5,
                     template_id: Optional[int] = None,
                     type_id: Optional[int] = None,
                     custom_fields: Optional[List[Dict[str, Any]]] = None,
                     properties: Optional[List[Dict[str, str]]] = None,
                     **kwargs) -> Optional[int]:
        """
        创建工单
            
        Returns:
            Optional[int]: 创建成功返回工单ID，失败返回None
        """
        try:
            # 构建请求数据
            data = {
                "title": title[:100],  # 限制100字符
                "content": content[:3000],  # 限制3000字符
                "priority": priority
            }
            
            # 添加用户标识（uid或userMobile至少一个）
            if uid:
                data["uid"] = uid[:64]
            elif user_mobile:
                data["userMobile"] = user_mobile[:128]
            else:
                print("[ERROR] 创建工单失败：必须提供 uid 或 userMobile")
                return None
            
            # 添加可选字段
            if user_email:
                data["userEmail"] = user_email[:255]
            if user_name:
                data["userName"] = user_name[:128]
            if staff_id:
                data["staffId"] = staff_id
            if template_id:
                data["templateId"] = template_id
            if type_id:
                data["typeId"] = type_id
            if custom_fields:
                data["customFields"] = custom_fields
            if properties:
                data["properties"] = properties
                
            # 添加其他kwargs参数
            for key, value in kwargs.items():
                if key not in data:
                    data[key] = value
            
            # 发送请求
            response = self._make_request("/ticket/create", data)
            
            if response and response.get("code") == 200:
                ticket_id = int(response.get("message", 0))
                print(f"[INFO] 工单创建成功，工单ID: {ticket_id}")
                return ticket_id
            else:
                print(f"[ERROR] 创建工单失败: {response}")
                return None
                
        except Exception as e:
            print(f"[ERROR] 创建工单异常: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def create_alert_ticket(self,
                           session_id: str,
                           alert_reason: str,
                           analysis_data: Dict[str, Any],
                           staff_name: str = "未知",
                           customer_name: str = "未知",
                           rule_level: str = "无",
                           member_id: Optional[str] = None,
                           staff_id: Optional[int] = None) -> Optional[int]:
        """
        创建告警工单
        
        Args:
            session_id (str): 会话ID
            alert_reason (str): 告警原因
            analysis_data (Dict): 分析数据
            staff_name (str): 客服名称
            customer_name (str): 客户名称
            rule_level (str): 告警等级
            member_id (str, optional): 会员ID
            staff_id (int, optional): 客服ID（必填，来自会话数据）
            
        Returns:
            Optional[int]: 工单ID
        """
        try:
            # 确定优先级
            priority_map = {
                "一级": 10,  # 非常紧急
                "二级": 8,   # 紧急
                "三级": 5    # 一般
            }
            priority = priority_map.get(rule_level, 5)
            
            # 构建工单标题
            title = f"[{rule_level}告警] {alert_reason} - 会话{session_id}"
            
            # 构建工单内容 - 只保留基本信息，避免与模板字段重复
            content_parts = [
                f"告警时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                f"客户: {customer_name}",
                f"会员ID: {member_id or '未知'}",
                "",
                "此工单为系统自动创建的质检预警工单，详细信息请查看下方自定义字段。",
                "",
                "如需处理此告警，请联系相关负责人员。"
            ]

            content = "\n".join(content_parts)
            
            # 构建附加属性
            properties = [
                {"key": "会话ID", "value": session_id},
                {"key": "告警等级", "value": rule_level},
                {"key": "告警时间", "value": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            ]
            
            # 验证必要参数
            if not staff_id:
                print("[ERROR] 创建告警工单失败：必须提供有效的 staff_id")
                return None
                
            # 使用质检预警工单模板
            template_id = config.QIYU_TEMPLATE_ID  # 质检预警工单模板ID
            
            # 构建自定义字段 - 按照用户示例格式
            custom_fields = []
            
            # 会话ID (字段ID: 5858688)
            custom_fields.append({
                "id": 5858688,
                "value": str(session_id)
            })
            
            # 告警类型 (字段ID: 5863570)
            custom_fields.append({
                "id": 5863570,
                "value": "会话质检预警"  # 固定为客服会话告警
            })
            
            # 会话客服 (字段ID: 5863571)
            custom_fields.append({
                "id": 5863571,
                "value": str(staff_name)
            })
            
            # 告警原因 (字段ID: 5863568)
            custom_fields.append({
                "id": 5863568,
                "value": str(alert_reason)
            })
            
            # 用户诉求 (字段ID: 5863569)
            user_demand = analysis_data.get('用户诉求', {})
            user_demand_parts = []
            if user_demand.get('用户核心诉求'):
                user_demand_parts.append(f"核心诉求: {user_demand['用户核心诉求']}")
            if user_demand.get('佐证'):
                user_demand_parts.append(f"佐证: {user_demand['佐证']}")
            user_demand_text = "\n".join(user_demand_parts) if user_demand_parts else ""
            custom_fields.append({
                "id": 5863569,
                "value": user_demand_text
            })
            
            # 会话基本信息 (字段ID: 5857648) - 重新定义为会话时间信息
            dialogue_mode = analysis_data.get('对话模式', {})
            basic_info_parts = []
            if dialogue_mode.get('会话开始时间'):
                basic_info_parts.append(f"会话开始时间: {dialogue_mode['会话开始时间']}")
            if dialogue_mode.get('会话结束时间'):
                basic_info_parts.append(f"会话结束时间: {dialogue_mode['会话结束时间']}")
            if dialogue_mode.get('会话持续时长'):
                basic_info_parts.append(f"会话持续时长: {dialogue_mode['会话持续时长']} 分钟")
            basic_info_text = "\n".join(basic_info_parts) if basic_info_parts else ""
            custom_fields.append({
                "id": 5857648,
                "value": basic_info_text
            })
            
            # 对话模式 (字段ID: 5861538) - 重新定义为对话轮次和效率
            dialogue_pattern_parts = []
            if dialogue_mode.get('对话轮次'):
                dialogue_pattern_parts.append(f"对话轮次: {dialogue_mode['对话轮次']}")
            if dialogue_mode.get('客服处理效率'):
                dialogue_pattern_parts.append(f"客服效率: {dialogue_mode['客服处理效率']}")
            dialogue_pattern_text = "\n".join(dialogue_pattern_parts) if dialogue_pattern_parts else ""
            custom_fields.append({
                "id": 5861538,
                "value": dialogue_pattern_text
            })
            
            # 风险点识别 (字段ID: 5865489)
            risk_info = analysis_data.get('风险点识别', {})
            risk_parts = []
            if risk_info.get('投诉风险'):
                risk_parts.append(f"投诉风险: {risk_info['投诉风险']}")
            if risk_info.get('投诉类型'):
                risk_parts.append(f"投诉类型: {risk_info['投诉类型']}")
            if risk_info.get('投诉渠道'):
                risk_parts.append(f"投诉渠道: {risk_info['投诉渠道']}")
            risk_text = "\n".join(risk_parts) if risk_parts else ""
            custom_fields.append({
                "id": 5865489,
                "value": risk_text
            })
            
            print(f"[DEBUG] 自定义字段数据: {custom_fields}")
            
            # 创建工单参数
            create_params = {
                'title': title,
                'content': content,
                'uid': member_id,
                'user_name': customer_name,
                'staff_id': staff_id,
                'priority': priority,
                'template_id': template_id,
                'custom_fields': custom_fields,
                'properties': properties
            }
            
            print(f"[DEBUG] ========== 创建工单参数 ==========")
            print(f"[DEBUG] 工单标题: {title}")
            print(f"[DEBUG] 模板ID: {template_id}")
            print(f"[DEBUG] 客服ID: {staff_id}")
            print(f"[DEBUG] 优先级: {priority}")
            print(f"[DEBUG] 用户ID: {member_id}")
            print(f"[DEBUG] 用户名: {customer_name}")
            print(f"[DEBUG] 内容长度: {len(content)} 字符")
            print(f"[DEBUG] 自定义字段数量: {len(custom_fields) if custom_fields else 0}")
            print(f"[DEBUG] 附加属性数量: {len(properties) if properties else 0}")
            print(f"[DEBUG] 完整参数:")
            for key, value in create_params.items():
                if key == 'custom_fields' and value:
                    print(f"[DEBUG]   {key}: {len(value)}个字段")
                elif key == 'content':
                    print(f"[DEBUG]   {key}: '{value[:100]}{'...' if len(value) > 100 else ''}'")
                else:
                    print(f"[DEBUG]   {key}: {value}")
            print(f"[DEBUG] =========================================")
            
            # 创建工单
            ticket_id = self.create_ticket(**create_params)
            
            return ticket_id
            
        except Exception as e:
            print(f"[ERROR] 创建告警工单失败: {e}")
            import traceback
            traceback.print_exc()
            return None


# 创建全局服务实例
qiyu_ticket_service = QiyuTicketService()