import time

import requests
import json
import config

import datetime
from datetime import datetime, timedelta
from store_data_to_sr import StarRocksStore
from llm_service import llm_service
from qiyu_ticket_service import qiyu_ticket_service

# Function to create the analysis card dictionary

def create_slack_blocks(analysis_data, session_id, reason, staff_name="未知", customer_name="未知", rule_level="无",
                        member_id=0):
    """
    根据分析数据构建Slack消息的Block结构。

    Args:
        analysis_data (dict): 大模型返回的分析结果字典。
        session_id (str): 会话 ID。
        reason (str): 触发告警的原因。
        staff_name (str): 客服名称。
        customer_name (str): 客户名称。
        rule_level (str): 告警等级。
        member_id (int): 会员ID。

    Returns:
        dict: Slack消息的Block结构。
    """
    try:
        print(f"[DEBUG] create_slack_blocks 开始执行，datetime类型: {type(datetime)}")
        print(f"[DEBUG] datetime.now 方法: {datetime.now}")
    except Exception as e:
        print(f"[DEBUG] create_slack_blocks 调试信息获取失败: {e}")
    # 确保session_id是字符串
    session_id = str(session_id)
    member_id = str(member_id)

    # 确定告警颜色和emoji
    if rule_level == '一级':
        color = "#ff0000"  # 红色
        emoji = "🔴"
    elif rule_level == '二级':
        color = "#ff8c00"  # 橙色
        emoji = "🟠"
    elif rule_level == '三级':
        color = "#0080ff"  # 蓝色
        emoji = "🔵"
    else:
        color = "#ff0000"  # 默认红色
        emoji = "🚨"

    # 截断过长的文本（Slack限制）
    def truncate_text(text, max_length=2000):
        if len(text) > max_length:
            return text[:max_length - 3] + "..."
        return text

    # 构建基本信息
    basic_info_text = f"*会话ID:* {session_id}\n*客服:* {staff_name}\n*客户:* {customer_name}\n*会员ID:* {member_id}"

    # 构建用户诉求
    user_request_text = "*用户诉求*\n"
    if analysis_data.get('用户诉求', {}).get('用户核心诉求'):
        core_demand = analysis_data['用户诉求']['用户核心诉求']
        user_request_text += f"核心诉求: {truncate_text(core_demand, 500)}\n"
    if analysis_data.get('用户诉求', {}).get('佐证'):
        evidence = analysis_data['用户诉求']['佐证']
        user_request_text += f"佐证: \"{truncate_text(evidence, 500)}\"\n"

    # 构建对话模式信息
    dialogue_mode = analysis_data.get('对话模式', {})
    dialogue_text = "*会话基本信息*\n"
    dialogue_text += f"会话开始时间: {dialogue_mode.get('会话开始时间', '未知')}\n"
    dialogue_text += f"会话结束时间: {dialogue_mode.get('会话结束时间', '未知')}\n"
    dialogue_text += f"会话持续时长: {dialogue_mode.get('会话持续时长', '未知')} 分钟\n"
    # dialogue_text += f"\n*对话模式*\n"
    dialogue_text += f"对话轮次: {dialogue_mode.get('对话轮次', '未知')}\n"
    # dialogue_text += f"客服效率: {dialogue_mode.get('客服处理效率', '未知')}\n"
    # if dialogue_mode.get('理由'):
    #     reason_text = dialogue_mode.get('理由')
    #     dialogue_text += f"理由: {truncate_text(reason_text, 300)}\n"

    # 构建风险点识别
    risk_identification = analysis_data.get('风险点识别', {})
    risk_text = "*风险点识别*\n"
    if risk_identification:
        risk_text += f"投诉风险: {risk_identification.get('投诉风险', '未知')}\n"
        risk_text += f"投诉类型: {risk_identification.get('投诉类型', '未知')}\n"
        risk_text += f"投诉渠道: {risk_identification.get('投诉渠道', '未知')}\n"

    # 构建Slack Block结构
    blocks = [
        {
            "type": "header",
            "text": {
                "type": "plain_text",
                "text": f"{emoji} 客服会话告警: {truncate_text(reason, 100)}",
                "emoji": True
            }
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": truncate_text(basic_info_text, 3000)
            }
        },
        {
            "type": "divider"
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": truncate_text(user_request_text, 3000)
            }
        },
        {
            "type": "divider"
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": truncate_text(dialogue_text, 3000)
            }
        },
        {
            "type": "divider"
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": truncate_text(risk_text, 3000)
            }
        },
        {
            "type": "divider"
        },
        {
            "type": "actions",
            "elements": [
                {
                    "type": "button",
                    "text": {
                        "type": "plain_text",
                        "text": "查看会话详情",
                        "emoji": True
                    },
                    "style": "primary",
                    "action_id": "view_session_details",
                    "value": session_id  # 确保是字符串
                }
            ]
        }
    ]

    return {
        "blocks": blocks,
        "attachments": [
            {
                "color": color,
                "blocks": [
                    {
                        "type": "context",
                        "elements": [
                            {
                                "type": "mrkdwn",
                                "text": f"告警等级: *{rule_level}* | 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                            }
                        ]
                    }
                ]
            }
        ]
    }


class SlackNotifier:
    """用于向Slack发送消息的类。"""
    
    def __init__(self, webhook_url=None):
        """
        初始化 SlackNotifier。
        
        Args:
            webhook_url (str): Slack Webhook URL，如果不提供则从配置文件读取
        """
        self.webhook_url = webhook_url or config.SLACK_WEBHOOK_URL
        self.channel = config.SLACK_CHANNEL
        self.enabled = config.SLACK_ENABLED
        self.bot_token = config.SLACK_BOT_TOKEN  # 添加Bot Token
        
        if not self.webhook_url and self.enabled:
            print("[WARN] Slack未配置Webhook URL，无法发送消息")
    
    def send_blocks_with_bot(self, blocks_content, channel=None):
        """
        使用Bot Token发送Slack Block消息（支持交互组件）
        
        Args:
            blocks_content (dict): Block消息内容
            channel (str): 频道ID，如果不提供则使用默认频道
            
        Returns:
            bool: 发送是否成功
        """
        if not self.enabled:
            print("[INFO] Slack告警未启用，跳过发送")
            return False
            
        if not self.bot_token:
            print("[ERROR] Slack Bot Token未配置，无法发送交互式消息")
            return False
            
        try:
            url = "https://slack.com/api/chat.postMessage"
            headers = {
                'Authorization': f'Bearer {self.bot_token}',
                'Content-Type': 'application/json'
            }
            
            # 准备payload
            payload = blocks_content.copy()
            target_channel = channel or self.channel
            if target_channel:
                payload["channel"] = target_channel
            else:
                print("[ERROR] 未指定Slack频道")
                return False
            
            print(f"[DEBUG] 使用Bot Token发送到频道: {target_channel}")
            print(f"[DEBUG] 消息内容: {json.dumps(payload, indent=2, ensure_ascii=False)}")
            
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            result = response.json()
            
            if result.get("ok"):
                print(f"[INFO] 成功使用Bot Token发送Slack消息到频道 {target_channel}")
                return True
            else:
                error_msg = result.get('error', 'Unknown error')
                print(f"[ERROR] 使用Bot Token发送Slack消息失败: {error_msg}")
                return False
                
        except Exception as e:
            print(f"[ERROR] 使用Bot Token发送Slack消息时发生错误: {e}")
            return False
    
    def send_blocks(self, blocks_content):
        """
        发送Slack Block消息（Webhook方式，不支持交互组件）
        
        Args:
            blocks_content (dict): Block消息内容
            
        Returns:
            bool: 发送是否成功
        """
        if not self.enabled:
            print("[INFO] Slack告警未启用，跳过发送")
            return False
            
        if not self.webhook_url:
            print("[ERROR] Slack Webhook URL未配置，无法发送消息")
            return False
            
        try:
            # 添加频道信息（如果需要）
            payload = blocks_content.copy()
            if self.channel:
                payload["channel"] = self.channel
            
            response = requests.post(
                self.webhook_url,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if response.status_code == 200:
                print(f"[INFO] 成功发送Slack消息")
                return True
            else:
                print(f"[ERROR] 发送Slack消息失败: HTTP {response.status_code}, {response.text}")
                return False
                
        except Exception as e:
            print(f"[ERROR] 发送Slack消息时发生错误: {e}")
            return False
    
    def send_alert(self, session_id, reason, analysis_data: dict, staff_name="未知", customer_name="未知", rule_level="无", member_id=0):
        """
        发送包含会话分析结果的Slack告警消息
        
        Args:
            session_id (str): 触发告警的会话 ID
            reason (str): 触发告警的原因描述
            analysis_data (dict): 大模型分析结果的字典
            staff_name (str): 客服名称，默认为"未知"
            customer_name (str): 客户名称，默认为"未知"
            rule_level (str): 告警等级
            member_id (int): 会员ID，默认为0

        Returns:
            bool: 发送是否成功
        """
        if not self.enabled:
            print("[INFO] Slack告警未启用，跳过发送")
            return False
        
        if not isinstance(analysis_data, dict):
            print(f"[ERROR] analysis_data 必须是字典类型，但收到了 {type(analysis_data)} 类型。无法生成Slack消息。")
            return False

        try:
            # 生成Slack Block结构
            blocks_content = create_slack_blocks(
                analysis_data=analysis_data,
                session_id=session_id,
                reason=reason,
                staff_name=staff_name,
                customer_name=customer_name,
                rule_level=rule_level,
                member_id=member_id
            )
            
            # 优先使用Bot Token发送交互式消息，如果失败则回退到Webhook
            print("[INFO] 尝试使用Bot Token发送交互式Slack消息...")
            if self.bot_token and self.send_blocks_with_bot(blocks_content):
                return True
            
            print("[WARN] Bot Token发送失败，回退到Webhook发送（不支持交互按钮）...")
            return self.send_blocks(blocks_content)
            
        except Exception as e:
            print(f"[ERROR] 生成或发送Slack告警时发生错误: {e}")
            import traceback
            print(f"[DEBUG] 完整错误堆栈: {traceback.format_exc()}")
            return False


def _get_member_history_data(member_id):
    """
    获取客户历史数据

    Args:
        member_id (str): 客户会员ID

    Returns:
        dict: 客户历史数据
    """
    try:
        # 使用全局的sr_store实例
        from store_data_to_sr import StarRocksStore
        import config

        # 创建数据库连接
        sr_store = StarRocksStore(
            host=config.STARROCKS_HOST,
            port=config.STARROCKS_PORT,
            user=config.STARROCKS_USER,
            password=config.STARROCKS_PASSWORD,
            database=config.STARROCKS_DATABASE
        )

        if not sr_store.connect():
            print("[ERROR] 无法连接到数据库")
            return None

        # 计算时间范围
        now = datetime.now()
        seven_days_ago = now - timedelta(days=7)
        two_days_ago = now - timedelta(days=2)

        from pymysql.cursors import DictCursor
        cursor = sr_store.conn.cursor(DictCursor)

        # 第一步：查询该member_id在最近的所有session_id（由于conversations表可能没有时间字段，先获取所有）
        sessions_query = """
        SELECT DISTINCT session_id
        FROM conversations
        WHERE member_id = %s
        """
        
        cursor.execute(sessions_query, (member_id,))
        session_results = cursor.fetchall()
        session_ids = [row['session_id'] for row in session_results]
        
        print(f"[DEBUG] 找到客户 {member_id} 的会话ID: {len(session_ids)} 个")

        # 第二步：基于session_id查询历史诉求（Top 30），使用user_complaints表中的时间字段过滤
        complaints = []
        if session_ids:
            session_ids_placeholder = ','.join(['%s'] * len(session_ids))
            complaints_query = f"""
            SELECT session_id, user_demand, demand_evidence, start_time
            FROM user_complaints
            WHERE session_id IN ({session_ids_placeholder}) AND start_time >= %s
            ORDER BY start_time DESC
            LIMIT 30
            """
            
            query_params = session_ids + [seven_days_ago.strftime('%Y-%m-%d %H:%M:%S')]
            cursor.execute(complaints_query, query_params)
            complaints_raw = cursor.fetchall()
            
            # 构建投诉数据结构
            for complaint in complaints_raw:
                complaints.append({
                    'session_id': complaint['session_id'],
                    'core_demand': complaint.get('user_demand', ''),
                    'deal_result': complaint.get('demand_evidence', ''),  # 使用佐证作为处理结果的替代
                    'create_time': complaint['start_time']  # 使用start_time作为创建时间
                })

        # 第三步：获取最近的聊天记录（由于conversations表可能没有时间字段，使用LIMIT控制数量）
        conversations_query = """
        SELECT session_id, conversation_text as content, staff_name as staffName
        FROM conversations
        WHERE member_id = %s
        ORDER BY created_time DESC
        LIMIT 100
        """

        cursor.execute(conversations_query, (member_id,))
        conversations_raw = cursor.fetchall()
        
        # 为conversations添加模拟的createTime字段
        conversations = []
        for conv in conversations_raw:
            conversations.append({
                'sessionId': conv['session_id'],
                'content': conv['content'],
                'createTime': now.strftime('%Y-%m-%d %H:%M:%S'),  # 使用当前时间作为默认值
                'staffName': conv['staffName']
            })

        cursor.close()
        sr_store.disconnect()

        print(f"[DEBUG] 获取到客户 {member_id} 的历史诉求 {len(complaints)} 条，聊天记录 {len(conversations)} 条")

        return {
            'member_id': member_id,
            'complaints': complaints,
            'conversations': conversations
        }

    except Exception as e:
        print(f"[ERROR] 获取客户历史数据失败: {e}")
        return None


class SlackBotHandler:
    """用于处理Slack机器人交互的类。"""
    
    def __init__(self, bot_token=None):
        """
        初始化 SlackBotHandler。
        
        Args:
            bot_token (str): Slack Bot Token，如果不提供则从配置文件读取
        """
        self.bot_token = bot_token or config.SLACK_BOT_TOKEN
        self.signing_secret = config.SLACK_SIGNING_SECRET
        self.bot_user_id = config.SLACK_BOT_USER_ID
        
        if not self.bot_token:
            print("[WARN] 未配置Slack Bot Token，无法使用机器人功能")
    
    def send_session_details(self, channel, session_id):
        """
        向指定频道发送会话详情
        
        Args:
            channel (str): Slack频道ID
            session_id (str): 会话ID
            
        Returns:
            bool: 发送是否成功
        """
        if not self.bot_token:
            print("[ERROR] 未配置Slack Bot Token，无法发送消息")
            return False
            
        try:
            # 获取会话记录
            from qiyu_session_service import get_session_messages, format_session_messages
            
            print(f"[DEBUG] 开始获取会话 {session_id} 的消息...")
            messages = get_session_messages(session_id)
            
            print(f"[DEBUG] 七鱼API响应: {messages}")
            
            # 检查API响应
            if not messages or not isinstance(messages, dict):
                print(f"[ERROR] 七鱼API响应为空或格式错误")
                return self._send_message(channel, f"获取会话 {session_id} 失败：API响应异常")
            
            # 检查响应码
            if messages.get('code') != 200:
                error_msg = messages.get('message', '未知错误')
                print(f"[ERROR] 七鱼API返回错误: {error_msg}")
                return self._send_message(channel, f"获取会话 {session_id} 失败：{error_msg}")
            
            # 检查是否有消息数据
            message_data = messages.get('data', [])
            if not message_data:
                print(f"[DEBUG] 会话 {session_id} 没有消息数据")
                return self._send_message(channel, f"会话 {session_id} 暂无消息记录")
            
            print(f"[DEBUG] 会话消息数量: {len(message_data)}")
            
            # 格式化消息
            formatted = format_session_messages(messages)
            formatted_text = "\n".join(formatted)
            
            print(f"[DEBUG] 格式化文本长度: {len(formatted_text)}")
            
            # 构建简单的文本消息
            message_text = f"会话 {session_id} 详情:\n```\n{formatted_text}\n```"
            
            # 如果消息较短，直接发送
            MAX_LENGTH = 4000
            if len(message_text) <= MAX_LENGTH:
                print(f"[DEBUG] 消息长度 {len(message_text)} <= {MAX_LENGTH}，直接发送")
                return self._send_message(channel, message_text)
            
            # 消息较长，分批发送
            print(f"[DEBUG] 消息过长({len(message_text)} > {MAX_LENGTH})，开始分批发送")
            return self._send_batched_messages(channel, session_id, formatted)
            
        except ImportError as e:
            print(f"[ERROR] 导入qiyu_session_service模块失败: {e}")
            return self._send_message(channel, "系统错误：无法访问会话服务")
        except Exception as e:
            print(f"[ERROR] 处理会话查询时出错: {e}")
            import traceback
            traceback.print_exc()
            return self._send_message(channel, f"处理会话 {session_id} 时出错: {str(e)}")
    
    def _send_batched_messages(self, channel, session_id, formatted_messages):
        """
        分批发送长会话详情
        
        Args:
            channel (str): Slack频道ID
            session_id (str): 会话ID
            formatted_messages (list): 格式化后的消息列表
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 分批发送消息
            MAX_LENGTH = 4000
            batch_num = 1
            current_batch = []
            message_index = 0  # 消息索引，从0开始
            total_messages = len(formatted_messages)  # 总消息数
            
            for message in formatted_messages:
                # 添加当前消息到批次中
                test_batch = current_batch + [message]
                test_text = "\n".join(test_batch)
                
                # 计算当前批次的消息范围
                batch_start = message_index - len(current_batch) + 1
                batch_end = message_index + 1
                
                # 构建测试消息（包含markdown格式和行号范围）
                if batch_num == 1:
                    # 第一条消息包含总体信息
                    test_message = f"会话 {session_id} 详情 (共{total_messages}条消息) - 第{batch_num}部分 (第{batch_start}-{batch_end}条):\n```\n{test_text}\n```"
                else:
                    test_message = f"第 {batch_num} 部分 (第{batch_start}-{batch_end}条):\n```\n{test_text}\n```"
                
                # 如果加上这条消息会超过限制，先发送当前批次
                if len(test_message) > MAX_LENGTH and current_batch:
                    # 发送当前批次
                    batch_text = "\n".join(current_batch)
                    current_batch_start = message_index - len(current_batch) + 1
                    current_batch_end = message_index
                    
                    if batch_num == 1:
                        # 第一条消息包含总体信息
                        message_text = f"会话 {session_id} 详情 (共{total_messages}条消息) - 第{batch_num}部分 (第{current_batch_start}-{current_batch_end}条):\n```\n{batch_text}\n```"
                    else:
                        message_text = f"第 {batch_num} 部分 (第{current_batch_start}-{current_batch_end}条):\n```\n{batch_text}\n```"
                    
                    if not self._send_message(channel, message_text):
                        print(f"[ERROR] 发送第 {batch_num} 批消息失败")
                        return False
                    
                    batch_num += 1
                    current_batch = [message]  # 重新开始新批次
                    
                    # 避免发送过快
                    import time
                    time.sleep(0.5)
                else:
                    # 可以添加到当前批次
                    current_batch.append(message)
                
                message_index += 1
            
            # 发送最后一批（如果有）
            if current_batch:
                batch_text = "\n".join(current_batch)
                batch_start = message_index - len(current_batch)
                batch_end = message_index - 1
                
                if batch_num == 1:
                    # 如果只有一批，包含总体信息
                    message_text = f"会话 {session_id} 详情 (共{total_messages}条消息) - 第{batch_num}部分 (第{batch_start + 1}-{batch_end + 1}条):\n```\n{batch_text}\n```"
                else:
                    message_text = f"第 {batch_num} 部分 (第{batch_start + 1}-{batch_end + 1}条):\n```\n{batch_text}\n```"
                
                if not self._send_message(channel, message_text):
                    print(f"[ERROR] 发送第 {batch_num} 批消息失败")
                    return False
            
            return True
            
        except Exception as e:
            print(f"[ERROR] 分批发送消息失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _send_message(self, channel, text):
        """发送简单文本消息"""
        return self._post_message(channel, {"text": text})
    
    def _send_blocks(self, channel, blocks):
        """发送Block消息"""
        return self._post_message(channel, {"blocks": blocks})
    
    def _post_message(self, channel, payload):
        """向Slack发送消息的通用方法"""
        try:
            url = "https://slack.com/api/chat.postMessage"
            headers = {
                'Authorization': f'Bearer {self.bot_token}',
                'Content-Type': 'application/json'
            }
            
            data = {
                "channel": channel,
                **payload
            }
            
            response = requests.post(url, headers=headers, json=data, timeout=30)
            result = response.json()
            
            if result.get("ok"):
                print(f"[INFO] 成功发送Slack消息到频道 {channel}")
                return True
            else:
                print(f"[ERROR] 发送Slack消息失败: {result.get('error', 'Unknown error')}")
                return False
                
        except Exception as e:
            print(f"[ERROR] 发送Slack消息时发生错误: {e}")
            return False
    
    def send_member_analysis(self, channel, member_id):
        """
        分析客户历史问题
        
        Args:
            channel (str): Slack频道ID
            member_id (str): 客户会员ID
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 获取客户历史数据
            customer_data = _get_member_history_data(member_id)
            
            if not customer_data:
                return self._send_message(channel, f"未找到客户 {member_id} 的历史记录")
            
            # 调用LLM服务进行分析
            print(f"[DEBUG] 准备调用LLM分析，客户数据: 投诉{len(customer_data.get('complaints', []))}条, 会话{len(customer_data.get('conversations', []))}条")
            
            analysis_text = llm_service.analyze_customer_history(
                member_id, 
                customer_data.get('complaints', []), 
                customer_data.get('conversations', [])
            )
            
            print(f"[DEBUG] LLM分析完成，结果类型: {type(analysis_text)}, 是否为空: {not analysis_text}")
            if analysis_text:
                print(f"[DEBUG] 分析结果长度: {len(analysis_text)}, 前100字符: {analysis_text[:100]}...")
                return self._send_member_analysis_blocks(channel, member_id, {'analysis_text': analysis_text})
            else:
                print(f"[ERROR] LLM分析返回空结果，可能的原因：API调用失败、响应解析失败或LLM服务异常")
                return self._send_message(channel, f"AI分析客户 {member_id} 历史问题失败：LLM服务返回空结果，请检查日志获取详细错误信息")
                
        except Exception as e:
            print(f"[ERROR] 处理客户分析时出错: {e}")
            return self._send_message(channel, f"处理客户分析时出错: {str(e)}")

    def handle_member_analysis_command(self, channel, user, member_id):
        """处理客户分析命令"""
        try:
            print(f"[DEBUG] 开始处理客户分析命令，member_id: {member_id}")
            
            # 获取客户历史数据
            customer_data = _get_member_history_data(member_id)
            
            if not customer_data:
                print(f"[DEBUG] 未获取到客户 {member_id} 的历史数据")
                return self._send_message(channel, f"❌ 未找到客户 {member_id} 的历史数据")
            
            print(f"[DEBUG] 获取到客户数据: 投诉 {len(customer_data.get('complaints', []))} 条, 会话 {len(customer_data.get('conversations', []))} 条")
            
            # 调用LLM分析
            from llm_service import llm_service
            analysis_result = llm_service.analyze_customer_history(
                member_id=member_id,
                complaints=customer_data.get('complaints', []),
                conversations=customer_data.get('conversations', [])
            )
            
            print(f"[DEBUG] LLM分析结果类型: {type(analysis_result)}")
            print(f"[DEBUG] LLM分析结果长度: {len(analysis_result) if analysis_result else 0} 字符")
            if analysis_result:
                print(f"[DEBUG] LLM分析结果前300字符: {analysis_result[:300]}...")
            
            if not analysis_result:
                print(f"[DEBUG] LLM分析失败，返回空结果")
                return self._send_message(channel, f"❌ 客户 {member_id} 分析失败，请稍后重试")
            
            # 发送分析结果
            print(f"[DEBUG] 准备发送Slack blocks消息")
            return self._send_member_analysis_blocks(channel, member_id, analysis_result)
            
        except Exception as e:
            print(f"[ERROR] 处理客户分析时出错: {str(e)}")
            import traceback
            print(f"[DEBUG] 完整错误堆栈: {traceback.format_exc()}")
            return self._send_message(channel, f"处理客户分析时出错: {str(e)}")

    def _send_member_analysis_blocks(self, channel, member_id, analysis_result):
        """发送客户分析结果"""
        try:
            print(f"[DEBUG] 开始构建Slack blocks，member_id: {member_id}")
            print(f"[DEBUG] 分析结果内容: {analysis_result}")
            
            # 提取分析文本内容
            if isinstance(analysis_result, dict) and 'analysis_text' in analysis_result:
                analysis_text = analysis_result['analysis_text']
            else:
                analysis_text = str(analysis_result)
            
            print(f"[DEBUG] 提取的分析文本长度: {len(analysis_text)}")
            
            # 转义 Slack Markdown 特殊字符，避免渲染干扰
            def escape_slack_markdown(text):
                """转义 Slack Markdown 特殊字符"""
                if not text:
                    return ""
                # 转义常见的 Slack Markdown 字符
                replacements = {
                    '*': '\\*',
                    '_': '\\_',
                    '~': '\\~',
                    '`': '\\`',
                    '>': '\\>',
                    '|': '\\|'
                }
                for char, escaped in replacements.items():
                    text = text.replace(char, escaped)
                return text
            
            # 转义特殊字符
            escaped_analysis_text = escape_slack_markdown(analysis_text)
            
            # 检查文本长度，Slack Block 限制约为3000字符
            MAX_BLOCK_LENGTH = 2800  # 留一些余量给其他内容
            
            if len(escaped_analysis_text) <= MAX_BLOCK_LENGTH:
                # 解析分析结果，提取结构化信息
                parsed_content = self._parse_analysis_content(analysis_text)
                
                # 构建更美观的Block Kit消息
                blocks = [
                    {
                        "type": "header",
                        "text": {
                            "type": "plain_text",
                            "text": f"🔍 客户历史分析报告 - {member_id}"
                        }
                    },
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": f"*客户ID:* `{member_id}`\n*分析时间:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                        }
                    },
                    {
                        "type": "divider"
                    }
                ]
                
                # 如果能解析出结构化内容，使用更好的格式
                if parsed_content:
                    if parsed_content.get('core_demands'):
                        blocks.append({
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": f"*🎯 核心诉求汇总:*\n{parsed_content['core_demands']}"
                            }
                        })
                        blocks.append({"type": "divider"})
                    
                    if parsed_content.get('repeat_issues'):
                        blocks.append({
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": f"*🔄 问题重复情况:*\n{parsed_content['repeat_issues']}"
                            }
                        })
                        blocks.append({"type": "divider"})
                    
                    if parsed_content.get('handling_results'):
                        blocks.append({
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": f"*📋 实际处理结果:*\n{parsed_content['handling_results']}"
                            }
                        })
                else:
                    # 如果无法解析，使用原始格式
                    blocks.append({
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": f"*📊 分析结果:*\n{escaped_analysis_text}"
                        }
                    })
                
                # 添加上下文信息
                blocks.append({
                    "type": "context",
                    "elements": [
                        {
                            "type": "mrkdwn",
                            "text": f"💡 *提示:* 基于客户历史会话和投诉记录生成的AI分析报告"
                        }
                    ]
                })
                
                print(f"[DEBUG] 构建的blocks数量: {len(blocks)}")
                print(f"[DEBUG] 分析文本适合单个Block，长度: {len(escaped_analysis_text)}")
                
                # 发送消息
                return self._send_blocks(channel, blocks)
                
            else:
                # 文本过长，需要分批发送
                print(f"[DEBUG] 分析文本过长({len(escaped_analysis_text)} > {MAX_BLOCK_LENGTH})，开始分批发送")
                return self._send_long_analysis_result(channel, member_id, escaped_analysis_text)
                
        except Exception as e:
            print(f"[ERROR] 发送客户分析blocks失败: {e}")
            import traceback
            print(f"[DEBUG] 完整错误堆栈: {traceback.format_exc()}")
            # fallback 到简单文本消息
            return self._send_message(channel, f"客户 {member_id} 分析完成，但消息格式化失败:\n{str(analysis_result)[:1000]}...")

    def _parse_analysis_content(self, analysis_text):
        """
        解析客户分析内容，提取结构化信息
        
        Args:
            analysis_text (str): 原始分析文本
            
        Returns:
            dict: 解析后的结构化内容
        """
        try:
            if not analysis_text or not isinstance(analysis_text, str):
                return None
                
            parsed = {}
            current_section = None
            current_content = []
            
            lines = analysis_text.split('\n')
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                    
                # 检测章节标题
                if '核心诉求' in line and ('汇总' in line or '诉求' in line):
                    if current_section and current_content:
                        parsed[current_section] = '\n'.join(current_content)
                    current_section = 'core_demands'
                    current_content = []
                elif '问题重复' in line and '情况' in line:
                    if current_section and current_content:
                        parsed[current_section] = '\n'.join(current_content)
                    current_section = 'repeat_issues'
                    current_content = []
                elif '实际处理' in line and '结果' in line:
                    if current_section and current_content:
                        parsed[current_section] = '\n'.join(current_content)
                    current_section = 'handling_results'
                    current_content = []
                elif line.startswith(('1.', '2.', '3.', '4.', '5.')) and ('*' in line):
                    # 检测章节开始（如：1. \*\*核心诉求汇总\*\*:）
                    if current_section and current_content:
                        parsed[current_section] = '\n'.join(current_content)
                    
                    if '核心诉求' in line:
                        current_section = 'core_demands'
                    elif '问题重复' in line:
                        current_section = 'repeat_issues'  
                    elif '实际处理' in line:
                        current_section = 'handling_results'
                    else:
                        current_section = 'other_content'
                    current_content = []
                else:
                    # 普通内容行
                    if current_section:
                        current_content.append(line)
                    else:
                        # 如果还没有确定章节，归类到other_content
                        if 'other_content' not in parsed:
                            parsed['other_content'] = line
                        else:
                            parsed['other_content'] += '\n' + line
            
            # 处理最后一个章节
            if current_section and current_content:
                parsed[current_section] = '\n'.join(current_content)
            
            # 清理空的章节
            parsed = {k: v for k, v in parsed.items() if v and v.strip()}
            
            print(f"[DEBUG] 解析分析内容结果: {list(parsed.keys())}")
            return parsed if parsed else None
            
        except Exception as e:
            print(f"[ERROR] 解析分析内容失败: {e}")
            return None

    def _send_long_analysis_result(self, channel, member_id, analysis_text):
        """分批发送长分析结果"""
        try:
            MAX_CHUNK_SIZE = 2800
            
            # 发送头部信息
            header_success = self._send_blocks(channel, [
                {
                    "type": "header",
                    "text": {
                        "type": "plain_text",
                        "text": f"🔍 客户历史分析报告 - {member_id}"
                    }
                },
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*客户ID:* `{member_id}`\n*分析时间:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                    }
                },
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": ":information_source: *注意：* 分析结果较长，将分批发送以确保完整显示"
                    }
                },
                {
                    "type": "divider"
                }
            ])
            
            if not header_success:
                print(f"[ERROR] 发送分析报告头部失败")
                return False
            
            # 分批发送分析内容
            chunks = []
            current_pos = 0
            chunk_num = 1
            
            while current_pos < len(analysis_text):
                chunk_end = min(current_pos + MAX_CHUNK_SIZE, len(analysis_text))
                chunk_text = analysis_text[current_pos:chunk_end]
                
                # 避免在单词中间截断，寻找最近的换行符或句号
                if chunk_end < len(analysis_text):
                    last_newline = chunk_text.rfind('\n')
                    last_period = chunk_text.rfind('。')
                    last_break = max(last_newline, last_period)
                    
                    if last_break > current_pos + MAX_CHUNK_SIZE * 0.8:  # 如果断点位置合理
                        chunk_end = current_pos + last_break + 1
                        chunk_text = analysis_text[current_pos:chunk_end]
                
                chunks.append((chunk_num, chunk_text))
                current_pos = chunk_end
                chunk_num += 1
            
            # 发送每个分块
            total_chunks = len(chunks)
            for chunk_num, chunk_text in chunks:
                # 尝试解析这一段的内容，看是否包含特定章节
                section_emoji = "📄"
                if "核心诉求" in chunk_text:
                    section_emoji = "🎯"
                elif "问题重复" in chunk_text:
                    section_emoji = "🔄"
                elif "实际处理" in chunk_text:
                    section_emoji = "📋"
                
                blocks = [
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": f"*{section_emoji} 分析结果 - 第{chunk_num}部分 (共{total_chunks}部分)*"
                        }
                    },
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": chunk_text
                        }
                    }
                ]
                
                # 如果不是最后一部分，添加分隔线和进度提示
                if chunk_num < total_chunks:
                    blocks.append({"type": "divider"})
                    blocks.append({
                        "type": "context",
                        "elements": [
                            {
                                "type": "mrkdwn",
                                "text": f"📊 进度: {chunk_num}/{total_chunks} | 继续阅读下一部分..."
                            }
                        ]
                    })
                else:
                    # 最后一部分，添加完成提示
                    blocks.append({
                        "type": "context",
                        "elements": [
                            {
                                "type": "mrkdwn",
                                "text": f"✅ 分析报告已完整发送 ({total_chunks}部分) | 💡 基于客户历史会话和投诉记录生成"
                            }
                        ]
                    })
                
                success = self._send_blocks(channel, blocks)
                if not success:
                    print(f"[ERROR] 发送第{chunk_num}部分分析结果失败")
                    return False
                
                # 避免发送过快
                import time
                time.sleep(0.8)
            
            print(f"[INFO] 成功分批发送分析结果，共{total_chunks}部分")
            return True
            
        except Exception as e:
            print(f"[ERROR] 分批发送长分析结果失败: {e}")
            import traceback
            print(f"[DEBUG] 完整错误堆栈: {traceback.format_exc()}")
            return False


class UnifiedNotifier:
    """统一通知器，使用 Slack 发送告警（单例模式）"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(UnifiedNotifier, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化统一通知器（仅在第一次创建时执行）"""
        if not UnifiedNotifier._initialized:
            self.slack_notifier = SlackNotifier()
            self.slack_bot_handler = SlackBotHandler()
            UnifiedNotifier._initialized = True
            print("[INFO] UnifiedNotifier 单例实例已初始化")
        else:
            print("[DEBUG] UnifiedNotifier 单例实例已存在，跳过初始化")
    
    def send_alert(self, session_id, reason, analysis_data: dict, staff_name="未知", customer_name="未知", rule_level="无", member_id=0, staff_id=None):
        """
        发送 Slack 告警并创建工单
        
        Args:
            session_id (str): 触发告警的会话 ID
            reason (str): 触发告警的原因描述
            analysis_data (dict): 大模型分析结果的字典
            staff_name (str): 客服名称，默认为"未知"
            customer_name (str): 客户名称，默认为"未知"
            rule_level (str): 告警等级
            member_id (int): 会员ID，默认为0
            staff_id (int): 客服ID，用于创建工单

        Returns:
            bool: 发送是否成功
        """
        try:
            # 1. 发送Slack告警
            slack_result = self.slack_notifier.send_alert(
                session_id=session_id,
                reason=reason,
                analysis_data=analysis_data,
                staff_name=staff_name,
                customer_name=customer_name,
                rule_level=rule_level,
                member_id=member_id
            )
            print(f"[INFO] Slack告警发送结果: {'成功' if slack_result else '失败'}")
            
            # 2. 创建工单（异步进行，不影响主流程）
            try:
                ticket_id = qiyu_ticket_service.create_alert_ticket(
                    session_id=session_id,
                    alert_reason=reason,
                    analysis_data=analysis_data,
                    staff_name=staff_name,
                    customer_name=customer_name,
                    rule_level=rule_level,
                    member_id=str(member_id) if member_id else None,
                    staff_id=staff_id
                )
                
                if ticket_id:
                    print(f"[INFO] 成功创建工单，工单ID: {ticket_id}")
                    
                    # 如果需要，可以在Slack消息中追加工单信息
                    if slack_result and self.slack_bot_handler.bot_token:
                        try:
                            # 发送工单创建成功的追加消息
                            ticket_message = f"📋 已创建工单 #{ticket_id}"
                            self.slack_bot_handler._send_message(
                                config.SLACK_CHANNEL,
                                ticket_message
                            )
                        except Exception as e:
                            print(f"[WARN] 发送工单追加消息失败: {e}")
                else:
                    print(f"[WARN] 创建工单失败，会话ID: {session_id}")
                    
            except Exception as e:
                print(f"[ERROR] 创建工单时出错: {e}，但不影响告警发送")
                
            return slack_result
            
        except Exception as e:
            print(f"[ERROR] 发送Slack告警时出错: {e}")
            return False

    def send_reminder(self, title, tasks_data):
        """
        发送任务提醒消息到Slack
        
        Args:
            title (str): 提醒标题
            tasks_data (list): 任务数据列表
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 构建Slack消息块
            blocks = [
                {
                    "type": "header",
                    "text": {
                        "type": "plain_text",
                        "text": "🚨 超时待处理任务提醒"
                    }
                },
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*{title}*"
                    }
                },
                {
                    "type": "divider"
                }
            ]
            
            # 为每个任务添加一个section
            for task in tasks_data:
                # 计算预期完成时间
                create_time = task['create_time']
                expected_time = create_time + datetime.timedelta(minutes=config.TASK_REMINDER_THRESHOLD)
                expected_time_str = expected_time.strftime("%Y-%m-%d %H:%M:%S")
                
                task_text = f"*会话ID:* {task['session_id']}\n"
                task_text += f"*责任人:* {task['assigned_staff']}\n"
                task_text += f"*预警类型:* {task['alert_type']}\n"
                task_text += f"*告警时间:* {task['create_time']}\n"
                task_text += f"*预期完成时间:* {expected_time_str}\n"
                task_text += f"*当前状态:* {task['task_status']}"
                
                blocks.append({
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": task_text
                    }
                })
                
                # 添加分隔线（除了最后一个任务）
                if task != tasks_data[-1]:
                    blocks.append({
                        "type": "divider"
                    })
            
            # 添加提示信息
            blocks.extend([
                {
                    "type": "divider"
                },
                {
                    "type": "context",
                    "elements": [
                        {
                            "type": "mrkdwn",
                            "text": "💡 *提示：* 请各任务分配人尽快完成超时未处理任务，请负责人跟踪进度"
                        }
                    ]
                }
            ])
            
            # 发送消息
            slack_result = self.slack_notifier.send_blocks({
                "blocks": blocks,
                "attachments": [
                    {
                        "color": "#ff0000",  # 红色表示紧急
                        "fallback": title
                    }
                ]
            })
            
            print(f"[INFO] Slack任务提醒发送结果: {'成功' if slack_result else '失败'}")
            return slack_result
            
        except Exception as e:
            print(f"[ERROR] 发送Slack任务提醒时出错: {e}")
            return False

