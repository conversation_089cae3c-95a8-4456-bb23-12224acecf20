#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
LLM服务模块
提供统一的LLM调用接口和常用的分析功能
"""

import requests
import json
import time
from typing import List, Dict, Any, Optional
import config


def extract_content(llm_response: Dict[str, Any]) -> str:
    """
    从LLM响应中提取内容

    Args:
        llm_response (Dict[str, Any]): LLM响应结果

    Returns:
        str: 提取的内容文本
    """
    try:
        print(f"[DEBUG] extract_content 接收的响应类型: {type(llm_response)}")
        print(f"[DEBUG] extract_content 接收的响应内容: {llm_response}")

        # 检查新的响应格式：{"status":0,"message":"","chat_reply":"..."}
        if 'chat_reply' in llm_response:
            content = llm_response.get('chat_reply', '')
            print(f"[DEBUG] 从 chat_reply 提取内容: {content[:100]}...")
            return content
        elif 'response' in llm_response:
            content = llm_response['response'].get('content', '')
            print(f"[DEBUG] 从 response.content 提取内容: {content[:100]}...")
            return content
        else:
            content = str(llm_response)
            print(f"[DEBUG] 直接转换为字符串: {content[:100]}...")
            return content
    except Exception as e:
        print(f"[ERROR] 提取LLM响应内容失败: {e}")
        import traceback
        print(f"[DEBUG] extract_content 错误堆栈: {traceback.format_exc()}")
        return '响应解析失败'


def extract_structured_content(llm_response: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    从LLM响应中提取结构化JSON内容

    Args:
        llm_response (Dict[str, Any]): LLM响应结果

    Returns:
        Optional[Dict[str, Any]]: 解析后的JSON数据，失败时返回None
    """
    try:
        print(f"[DEBUG] extract_structured_content 接收的响应类型: {type(llm_response)}")

        # 先提取文本内容
        content = extract_content(llm_response)
        if not content or content == '响应解析失败':
            print(f"[ERROR] 无法提取有效的文本内容")
            return None

        print(f"[DEBUG] 提取的原始内容: {content[:200]}...")

        # 清理可能的markdown代码块标记
        cleaned_content = content.strip()
        if cleaned_content.startswith('```json'):
            cleaned_content = cleaned_content[7:]
        if cleaned_content.startswith('```'):
            cleaned_content = cleaned_content[3:]
        if cleaned_content.endswith('```'):
            cleaned_content = cleaned_content[:-3]
        cleaned_content = cleaned_content.strip()

        print(f"[DEBUG] 清理后的内容: {cleaned_content[:200]}...")

        # 解析JSON
        structured_data = json.loads(cleaned_content)
        print(f"[DEBUG] 成功解析JSON，包含字段: {list(structured_data.keys())}")
        return structured_data

    except json.JSONDecodeError as e:
        print(f"[ERROR] JSON解析失败: {e}")
        print(f"[DEBUG] 原始内容: {content[:500]}...")
        return None
    except Exception as e:
        print(f"[ERROR] 提取结构化内容失败: {e}")
        import traceback
        print(f"[DEBUG] 错误堆栈: {traceback.format_exc()}")
        return None


def _build_customer_analysis_prompt(member_id: str, complaints: List[Dict], conversations: List[Dict]) -> str:
    """构建客户分析的数据摘要"""
    data_summary = f"""
客户ID: {member_id}
最近7天历史诉求数量: {len(complaints)}
最近2天聊天记录数量: {len(conversations)}

历史诉求记录:
"""

    # 添加历史诉求
    for i, complaint in enumerate(complaints, 1):
        data_summary += f"""
{i}. 会话ID: {complaint.get('session_id', 'N/A')}
时间: {complaint.get('create_time', 'N/A')}
核心诉求: {complaint.get('core_demand', 'N/A')}
解决结果: {complaint.get('deal_result', 'N/A')}
"""

    data_summary += f"""

最近聊天记录:
"""

    # 添加最近聊天记录
    for i, conv in enumerate(conversations, 1):
        content = conv.get('content', '') if conv.get('content') else ''
        data_summary += f"""
{i}. 会话ID: {conv.get('sessionId', 'N/A')}
时间: {conv.get('createTime', 'N/A')}
客服: {conv.get('staffName', 'N/A')}
内容: {content}
"""

    return data_summary


def _build_session_quality_prompt(session_id: str, session_data: Dict[str, Any]) -> str:
    """构建会话质量分析的数据摘要"""
    data_summary = f"""
会话ID: {session_id}
会话数据: {json.dumps(session_data, ensure_ascii=False, indent=2)[:2000]}
"""
    return data_summary


class LLMService:
    """LLM服务客户端，用于调用内部LLM API"""
    
    def __init__(self):
        """初始化LLM服务客户端"""
        self.api_url = config.LLM_SERVICE_URL
        self.timeout = config.LLM_SERVICE_TIMEOUT
        self.headers = {
            'Content-Type': 'application/json',
            'x-stage': 'canary'
        }
        
        # 配置检查
        if not self.api_url:
            print(f"[ERROR] LLM_SERVICE_URL 未配置！请设置环境变量 LLM_SERVICE_URL")
        else:
            print(f"[INFO] LLM服务初始化完成 - 服务URL: {self.api_url}, 超时: {self.timeout}秒")

    def chat_completion(self, messages: List[Dict[str, str]]) -> Optional[Dict[str, Any]]:
        """
        调用内部LLM服务进行对话补全

        Args:
            messages (List[Dict[str, str]]): 消息列表

        Returns:
            Optional[Dict[str, Any]]: LLM响应结果
        """
        try:
            print(f"[DEBUG] chat_completion 开始调用，消息数量: {len(messages)}")
            print(f"[DEBUG] 调用LLM服务URL: {self.api_url}")
            
            # 检查配置
            if not self.api_url:
                print(f"[ERROR] LLM服务URL未配置，无法调用API")
                return None
            
            # 构建请求负载
            payload = {
                "service": "lb.algo.chat.proxy",
                "method": "AlgoChatProxy.ChatCompletion",
                "request": {
                    "messages": messages,
                    "is_deepseek": True,
                    "model": "DeepSeek-R1"
                }
            }

            print(f"[DEBUG] 请求负载: {json.dumps(payload, ensure_ascii=False, indent=2)}")

            # 发送请求
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=payload,
                timeout=self.timeout
            )

            print(f"[DEBUG] HTTP响应状态码: {response.status_code}")
            print(f"[DEBUG] HTTP响应头: {dict(response.headers)}")

            if response.status_code == 200:
                result = response.json()
                print(f"[DEBUG] 解析的JSON响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                return result
            else:
                print(f"[ERROR] LLM API调用失败: HTTP {response.status_code}")
                print(f"[DEBUG] 响应文本: {response.text}")
                return None

        except requests.RequestException as e:
            print(f"[ERROR] LLM API请求异常: {e}")
            import traceback
            print(f"[DEBUG] 请求异常堆栈: {traceback.format_exc()}")
            return None
        except json.JSONDecodeError as e:
            print(f"[ERROR] LLM API响应JSON解析失败: {e}")
            print(f"[DEBUG] 原始响应: {response.text if 'response' in locals() else 'No response'}")
            return None
        except Exception as e:
            print(f"[ERROR] LLM API调用失败: {e}")
            import traceback
            print(f"[DEBUG] 通用异常堆栈: {traceback.format_exc()}")
            return None

    def analyze_customer_history(self, member_id: str, complaints: List[Dict], conversations: List[Dict]) -> Optional[str]:
        """
        分析客户历史问题

        Args:
            member_id (str): 客户会员ID
            complaints (List[Dict]): 历史投诉记录
            conversations (List[Dict]): 历史会话记录

        Returns:
            Optional[str]: 分析结果文本（为了向后兼容）
        """
        try:
            # 构建数据摘要
            data_summary = _build_customer_analysis_prompt(member_id, complaints, conversations)
            
            print(f"[DEBUG] 构建的数据摘要长度: {len(data_summary)} 字符")
            print(f"[DEBUG] 数据摘要前500字符: {data_summary[:500]}...")
            
            # 构建消息
            messages = [
                {
                    "role": "system",
                    "content": """你是一个客户服务记录整理专家。请基于客户的历史记录，客观地整理以下实际发生的事实，并以JSON格式返回结果。

请严格按照以下JSON格式返回分析结果：

```json
{
  "core_demands": [
    {
      "demand": "具体诉求描述",
      "sessions": ["相关会话ID列表"]
    }
  ],
  "repeated_issues": {
    "has_repeated": true/false,
    "details": [
      {
        "issue": "重复问题描述",
        "sessions": ["相关会话ID列表"]
      }
    ]
  },
  "resolution_status": [
    {
      "issue": "问题描述",
      "status": "已解决/未解决/处理中",
      "session_id": "会话ID"
    }
  ],
  "formatted_summary": "格式化的中文总结，用于展示"
}
```

要求：
1. 只基于记录中的实际内容进行整理，不要推测或添加主观判断
2. 必须返回有效的JSON格式
3. formatted_summary字段应包含条理清晰的中文总结"""
                },
                {
                    "role": "user",
                    "content": f"请整理以下客户的历史记录中的实际情况：\n\n{data_summary}"
                }
            ]
            
            print(f"[DEBUG] 发送给LLM的消息数量: {len(messages)}")
            print(f"[DEBUG] 系统消息长度: {len(messages[0]['content'])} 字符")
            print(f"[DEBUG] 用户消息长度: {len(messages[1]['content'])} 字符")
            
            # 调用LLM
            response = self.chat_completion(messages)

            print(f"[DEBUG] LLM原始响应类型: {type(response)}")
            print(f"[DEBUG] LLM原始响应内容: {response}")

            if response:
                # 尝试提取结构化数据
                structured_data = extract_structured_content(response)

                if structured_data:
                    print(f"[DEBUG] 成功提取结构化数据，字段: {list(structured_data.keys())}")

                    # 验证必要字段是否存在
                    required_fields = ['core_demands', 'repeated_issues', 'resolution_status', 'formatted_summary']
                    missing_fields = [field for field in required_fields if field not in structured_data]

                    if missing_fields:
                        print(f"[WARNING] 结构化数据缺少必要字段: {missing_fields}")
                        # 回退到原始文本提取
                        extracted_content = extract_content(response)
                        return extracted_content if extracted_content != '响应解析失败' else None

                    # 返回格式化的总结文本（保持向后兼容）
                    formatted_summary = structured_data.get('formatted_summary', '')
                    if formatted_summary:
                        print(f"[DEBUG] 返回格式化总结，长度: {len(formatted_summary)} 字符")
                        return formatted_summary
                    else:
                        print(f"[WARNING] 结构化数据中没有formatted_summary字段")
                        # 生成简单的文本总结
                        return self._generate_fallback_summary(structured_data)
                else:
                    print(f"[WARNING] 无法解析结构化数据，回退到原始文本提取")
                    # 回退到原始文本提取
                    extracted_content = extract_content(response)
                    if extracted_content and extracted_content.strip() and extracted_content != '响应解析失败':
                        return extracted_content
                    else:
                        print(f"[ERROR] 提取的内容无效: '{extracted_content}'")
                        return None
            else:
                print("[ERROR] LLM API调用失败，响应为空。可能原因：网络错误、API服务不可用、认证失败等")
                return None
                
        except Exception as e:
            print(f"[ERROR] 分析客户历史问题失败: {e}")
            import traceback
            print(f"[DEBUG] 完整错误堆栈: {traceback.format_exc()}")
            return None

    def _generate_fallback_summary(self, structured_data: Dict[str, Any]) -> str:
        """
        从结构化数据生成回退的文本总结

        Args:
            structured_data (Dict[str, Any]): 结构化分析数据

        Returns:
            str: 格式化的文本总结
        """
        try:
            summary_parts = []

            # 核心诉求汇总
            core_demands = structured_data.get('core_demands', [])
            if core_demands:
                summary_parts.append("**核心诉求汇总：**")
                for i, demand in enumerate(core_demands, 1):
                    demand_text = demand.get('demand', '未知诉求')
                    sessions = demand.get('sessions', [])
                    summary_parts.append(f"{i}. {demand_text} (涉及会话: {', '.join(sessions)})")

            # 问题重复情况
            repeated_issues = structured_data.get('repeated_issues', {})
            summary_parts.append("\n**问题重复情况：**")
            if repeated_issues.get('has_repeated', False):
                details = repeated_issues.get('details', [])
                for detail in details:
                    issue = detail.get('issue', '未知问题')
                    sessions = detail.get('sessions', [])
                    summary_parts.append(f"- {issue} (出现在会话: {', '.join(sessions)})")
            else:
                summary_parts.append("- 未发现重复问题")

            # 处理结果
            resolution_status = structured_data.get('resolution_status', [])
            if resolution_status:
                summary_parts.append("\n**处理结果：**")
                for status in resolution_status:
                    issue = status.get('issue', '未知问题')
                    status_text = status.get('status', '未知状态')
                    session_id = status.get('session_id', 'N/A')
                    summary_parts.append(f"- {issue}: {status_text} (会话ID: {session_id})")

            return "\n".join(summary_parts)

        except Exception as e:
            print(f"[ERROR] 生成回退总结失败: {e}")
            return "数据解析失败，无法生成总结"

    def analyze_customer_history_structured(self, member_id: str, complaints: List[Dict], conversations: List[Dict]) -> Optional[Dict[str, Any]]:
        """
        分析客户历史问题并返回结构化数据

        Args:
            member_id (str): 客户会员ID
            complaints (List[Dict]): 历史投诉记录
            conversations (List[Dict]): 历史会话记录

        Returns:
            Optional[Dict[str, Any]]: 结构化的分析结果
        """
        try:
            # 构建数据摘要
            data_summary = _build_customer_analysis_prompt(member_id, complaints, conversations)

            print(f"[DEBUG] 构建的数据摘要长度: {len(data_summary)} 字符")
            print(f"[DEBUG] 数据摘要前500字符: {data_summary[:500]}...")

            # 构建消息（使用相同的系统提示词）
            messages = [
                {
                    "role": "system",
                    "content": """你是一个客户服务记录整理专家。请基于客户的历史记录，客观地整理以下实际发生的事实，并以JSON格式返回结果。

请严格按照以下JSON格式返回分析结果：

```json
{
  "core_demands": [
    {
      "demand": "具体诉求描述",
      "sessions": ["相关会话ID列表"]
    }
  ],
  "repeated_issues": {
    "has_repeated": true/false,
    "details": [
      {
        "issue": "重复问题描述",
        "sessions": ["相关会话ID列表"]
      }
    ]
  },
  "resolution_status": [
    {
      "issue": "问题描述",
      "status": "已解决/未解决/处理中",
      "session_id": "会话ID"
    }
  ],
  "formatted_summary": "格式化的中文总结，用于展示"
}
```

要求：
1. 只基于记录中的实际内容进行整理，不要推测或添加主观判断
2. 必须返回有效的JSON格式
3. formatted_summary字段应包含条理清晰的中文总结"""
                },
                {
                    "role": "user",
                    "content": f"请整理以下客户的历史记录中的实际情况：\n\n{data_summary}"
                }
            ]

            # 调用LLM
            response = self.chat_completion(messages)

            if response:
                structured_data = extract_structured_content(response)
                if structured_data:
                    print(f"[DEBUG] 成功获取结构化数据: {list(structured_data.keys())}")
                    return structured_data
                else:
                    print(f"[ERROR] 无法解析结构化数据")
                    return None
            else:
                print("[ERROR] LLM API调用失败")
                return None

        except Exception as e:
            print(f"[ERROR] 分析客户历史问题（结构化）失败: {e}")
            import traceback
            print(f"[DEBUG] 完整错误堆栈: {traceback.format_exc()}")
            return None


# 创建全局LLM服务实例
llm_service = LLMService()