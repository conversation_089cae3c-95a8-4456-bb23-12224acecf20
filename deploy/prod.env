# 环境标识
ENVIRONMENT=prod

# 七鱼 API 配置
QIYU_APPKEY=6b647f0794b7065adc35551577f5939f
QIYU_ENCRYPT_KEY=D4AE7FD636304DC7BDAD5DAB0CE3B319
# 七鱼工单模板 id
QIYU_TEMPLATE_ID=5858688

# Prompt Service API 配置
PROMPT_SERVICE_ID=140
PROMPT_SERVICE_API_URL=https://fastflow.longbridge-inc.com/apps/node/prompts_service
PROMPT_SERVICE_READ_TIMEOUT=120
GRPC_TIME=240

# LLM 服务配置
LLM_SERVICE_URL=http://proxy-inner.prod-hk.lbkrs.com:8082/rpc
LLM_SERVICE_TIMEOUT=50

# 并发处理配置
MAX_CONCURRENT_THREADS=8

# 飞书机器人配置
FEISHU_APP_ID=cli_a89b4d3c423c500d
FEISHU_APP_SECRET=7vE34GJc1fv0u2m8lHpXQbVgrC3iCB5S
FEISHU_CHAT_ID=oc_cd0845b63116ed844ffab2db9143fb49
FEISHU_ENCRYPT_KEY=
FEISHU_VERIFICATION_TOKEN=73gN62Kej192uStU91tKTbY3hUqJL4mj
FEISHU_BOT_NAME=客服会话告警机器人
FEISHU_TASKLIST_GUID=ed99ce39-3259-40c9-9d2f-2be4ddf5b35f

# Slack 配置 - HTTP 回调模式
SLACK_WEBHOOK_URL=*********************************************************************************
SLACK_CHANNEL=#ai-vox-inspect
SLACK_ENABLED=True
SLACK_BOT_TOKEN=*********************************************************
SLACK_BOT_USER_ID=U092YB6DGEM
SLACK_SIGNING_SECRET=325b9b166d7d82a782a72fdc6cbbd118

# 数据处理目录
BASE_DIR=.

# StarRocks 数据库配置
STARROCKS_HOST=fe-c-96436b24d25e4bfd-internal.starrocks.aliyuncs.com
STARROCKS_PORT=9030
STARROCKS_USER=ai_rw_tz8
STARROCKS_PASSWORD=VE#WUU\&lBLk6m2
STARROCKS_DATABASE=ai_service

# 其他配置
POLL_INTERVAL_SECONDS=10
MAX_POLL_ATTEMPTS=30

# 任务检查器配置
CHECKER_INTERVAL=300
TASK_REMINDER_THRESHOLD=4320
CHECKER_DEFAULT_TIME=10:00 