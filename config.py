# config.py
import os

def get_env_bool(key: str, default: bool = False) -> bool:
    """获取布尔类型的环境变量"""
    value = os.getenv(key, str(default)).lower()
    return value in ('true', '1', 'yes', 'on')

def get_env_int(key: str, default: int = 0) -> int:
    """获取整数类型的环境变量"""
    try:
        return int(os.getenv(key, str(default)))
    except ValueError:
        return default



# 七鱼 API 配置
# 警告: 请勿将敏感信息直接提交到版本控制系统。
# 建议使用环境变量或其他安全方式管理这些密钥。
QIYU_APPKEY = os.getenv('QIYU_APPKEY')
QIYU_ENCRYPT_KEY = os.getenv('QIYU_ENCRYPT_KEY')

# Prompt Service API 配置
PROMPT_SERVICE_ID = get_env_int('PROMPT_SERVICE_ID', 139)
# PROMPT_SERVICE_ID = 140 # 线上
PROMPT_SERVICE_API_URL = os.getenv('PROMPT_SERVICE_API_URL', "https://fastflow.longbridge-inc.com/apps/node/prompts_service")
# Prompt Service 读取超时时间（秒）
PROMPT_SERVICE_READ_TIMEOUT = get_env_int('PROMPT_SERVICE_READ_TIMEOUT', 120)
GRPC_TIME = get_env_int('GRPC_TIME', 240)

# LLM 服务配置
LLM_SERVICE_URL = os.getenv('LLM_SERVICE_URL')
LLM_SERVICE_TIMEOUT = get_env_int('LLM_SERVICE_TIMEOUT', 50)

# 新增：并发处理配置
MAX_CONCURRENT_THREADS = get_env_int('MAX_CONCURRENT_THREADS', 8) # 同时请求 Prompt Service 的最大线程数

# Slack 告警配置
SLACK_ENABLED = get_env_bool('SLACK_ENABLED', True)   # 设置为 True 启用Slack告警
SLACK_WEBHOOK_URL = os.getenv('SLACK_WEBHOOK_URL')  # Slack Webhook URL 用于发送告警消息
SLACK_CHANNEL = os.getenv('SLACK_CHANNEL')  # 默认频道，可选
SLACK_BOT_TOKEN = os.getenv('SLACK_BOT_TOKEN')  # Bot User OAuth Token
SLACK_SIGNING_SECRET = os.getenv('SLACK_SIGNING_SECRET')  # Signing Secret 用于验证请求
SLACK_BOT_USER_ID = os.getenv('SLACK_BOT_USER_ID')  # Bot 的用户 ID

# Socket Mode 配置
SLACK_APP_TOKEN = os.getenv('SLACK_APP_TOKEN')  # App-Level Token (以 xapp- 开头)

# 数据处理目录
BASE_DIR = os.getenv('BASE_DIR', '.') # 替换为你的基础数据目录
DOWNLOAD_DIR = f"{BASE_DIR}/downloads"
EXTRACT_DIR = f"{BASE_DIR}/extracted_data"
# 新增：分析结果输出目录
ANALYSIS_OUTPUT_DIR = f"{BASE_DIR}/analysis_results"

# StarRocks 数据库配置
# STARROCKS_HOST = "fe-c-96436b24d25e4bfd-internal.starrocks.aliyuncs.com"  # 线上 StarRocks 数据库主机地址
STARROCKS_HOST = os.getenv('STARROCKS_HOST')
# STARROCKS_HOST = "fe-c-0e33f105c10e0e51.starrocks.aliyuncs.com"  # 测试 debug 地址
STARROCKS_PORT = get_env_int('STARROCKS_PORT', 9030)  # StarRocks 数据库端口
# STARROCKS_USER = "ai_rw_tz8"  # StarRocks 数据库用户名
STARROCKS_USER = os.getenv('STARROCKS_USER')
# STARROCKS_PASSWORD = "VE#WUU&lBLk6m2"  # StarRocks 数据库密码
STARROCKS_PASSWORD = os.getenv('STARROCKS_PASSWORD')
# STARROCKS_DATABASE = "ai_service"  # StarRocks 数据库名称
STARROCKS_DATABASE = os.getenv('STARROCKS_DATABASE')
# STARROCKS_DATABASE = "test"  # 测试 StarRocks 数据库名称

# 其他配置...
# 例如，轮询间隔、最大重试次数等
POLL_INTERVAL_SECONDS = get_env_int('POLL_INTERVAL_SECONDS', 10)
MAX_POLL_ATTEMPTS = get_env_int('MAX_POLL_ATTEMPTS', 30)

# 新增：任务检查器配置
CHECKER_INTERVAL = get_env_int('CHECKER_INTERVAL', 300)  # 检查器轮询间隔（秒），默认5分钟
TASK_REMINDER_THRESHOLD = get_env_int('TASK_REMINDER_THRESHOLD', 4320)  # 任务提醒阈值（分钟），超过此时间未处理的任务将发送提醒
CHECKER_DEFAULT_TIME = os.getenv('CHECKER_DEFAULT_TIME', '10:00')  # 任务检查器默认执行时间（24小时制）

# 验证必要的配置
def validate_config():
    """验证必要的配置项是否存在"""
    required_configs = [
        ('QIYU_APPKEY', QIYU_APPKEY),
        ('QIYU_ENCRYPT_KEY', QIYU_ENCRYPT_KEY),
        ('LLM_SERVICE_URL', LLM_SERVICE_URL),
        ('STARROCKS_HOST', STARROCKS_HOST),
        ('STARROCKS_USER', STARROCKS_USER),
        ('STARROCKS_PASSWORD', STARROCKS_PASSWORD)
    ]
    
    missing_configs = [name for name, value in required_configs if not value]
    if missing_configs:
        print(f"错误: 缺少必要的配置项: {', '.join(missing_configs)}")
        print("请检查环境变量配置")
        return False
    return True

def print_config_info():
    """打印当前配置信息（隐藏敏感信息）"""
    env_type = os.getenv('ENVIRONMENT', 'unknown')
    print(f"当前环境: {env_type}")
    print(f"Prompt Service ID: {PROMPT_SERVICE_ID}")
    print(f"LLM Service URL: {LLM_SERVICE_URL}")
    print(f"Slack 通道: {SLACK_CHANNEL}")
    print(f"Slack 启用: {SLACK_ENABLED}")
    print(f"StarRocks 数据库: {STARROCKS_DATABASE}")
    print(f"并发线程数: {MAX_CONCURRENT_THREADS}")

# 在模块加载时验证配置
if __name__ == "__main__":
    if validate_config():
        print("配置验证通过")
        print_config_info()
    else:
        print("配置验证失败，请检查环境变量设置")
else:
    # 静默验证，只在配置错误时打印
    if not validate_config():
        print("配置验证失败，请检查环境变量设置")