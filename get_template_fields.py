#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
获取七鱼工单模板字段信息
"""

import time
import hashlib
import json
import requests

def get_template_fields(template_id):
    """获取指定模板的字段信息"""
    
    # 配置
    app_key = '6b647f0794b7065adc35551577f5939f'
    app_secret = 'D4AE7FD636304DC7BDAD5DAB0CE3B319'
    
    # 请求数据
    request_data = {"templateId": template_id}
    request_json = json.dumps(request_data, separators=(',', ':'))
    
    # 计算请求json的md5
    request_md5 = hashlib.md5(request_json.encode('utf-8')).hexdigest().lower()
    
    # 生成时间戳
    timestamp = int(time.time())
    
    # 计算checksum: SHA1(app_secret + request_md5 + timestamp)
    checksum_string = f"{app_secret}{request_md5}{timestamp}"
    checksum = hashlib.sha1(checksum_string.encode('utf-8')).hexdigest().lower()
    
    # 请求参数
    params = {
        "appKey": app_key,
        "time": timestamp,
        "checksum": checksum
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json; charset=utf-8"
    }
    
    # URL
    url = "https://qiyukf.com/openapi/v2/ticket/template/fields"
    
    print("=== 请求参数 ===")
    print(f"URL: {url}")
    print(f"Params: {params}")
    print(f"Request JSON: {request_json}")
    print(f"Request MD5: {request_md5}")
    print(f"Checksum: {checksum}")
    
    try:
        response = requests.post(
            url,
            params=params,
            headers=headers,
            data=request_json,
            timeout=30
        )
        
        print(f"\n=== 响应结果 ===")
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                # 解析字段列表
                fields = json.loads(result.get("message", "[]"))
                return fields
            else:
                print(f"API错误: {result}")
                return None
        else:
            print(f"HTTP错误: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"请求异常: {e}")
        return None

def display_fields(fields, template_id):
    """显示字段信息"""
    
    if not fields:
        print(f"❌ 模板 {template_id} 没有字段信息")
        return
    
    print(f"\n🎯 模板 {template_id} 的字段信息:")
    print(f"共 {len(fields)} 个字段")
    print("-" * 80)
    
    for i, field in enumerate(fields, 1):
        field_id = field.get('fieldId', 'Unknown')
        field_name = field.get('name', 'Unknown')
        field_type = field.get('type', 'Unknown')
        field_required = field.get('required', False)
        field_default = field.get('defaultValue', '')
        field_options = field.get('options', [])
        
        print(f"{i:2d}. 字段ID: {field_id}")
        print(f"    名称: {field_name}")
        print(f"    类型: {field_type}")
        print(f"    必填: {'是' if field_required else '否'}")
        if field_default:
            print(f"    默认值: {field_default}")
        if field_options:
            print(f"    选项: {field_options}")
        print()

def main():
    """主函数"""
    
    print("获取七鱼工单模板字段")
    print("=" * 40)
    
    # 质检预警工单模板ID
    template_id = 6085540
    
    print(f"正在获取模板 {template_id} (质检预警工单) 的字段信息...")
    
    # 获取字段信息
    fields = get_template_fields(template_id)
    
    # 显示字段信息
    display_fields(fields, template_id)

if __name__ == "__main__":
    main()